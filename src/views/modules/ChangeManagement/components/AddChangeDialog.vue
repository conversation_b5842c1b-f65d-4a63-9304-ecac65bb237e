<template>
  <div class="add-project-dialog">
    <el-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-width="100px"
      class="add-project-form"
    >
      <el-form-item label="项目编号" prop="projectCode">
        <el-select
          v-model="formData.projectCode"
          placeholder="请选择项目编号"
          filterable
          clearable
          @change="handleProjectCodeChange"
          class="form-select"
        >
          <el-option
            v-for="item in projectOptions"
            :key="item.projectCode"
            :label="item.projectCode"
            :value="item.projectCode"
          />
        </el-select>
      </el-form-item>

      <el-form-item label="项目名称" prop="projectName">
        <el-select
          v-model="formData.projectName"
          placeholder="请选择项目名称"
          filterable
          clearable
          @change="handleProjectNameChange"
          class="form-select"
        >
          <el-option
            v-for="item in projectOptions"
            :key="item.projectCode"
            :label="item.projectName"
            :value="item.projectName"
          />
        </el-select>
      </el-form-item>
    </el-form>

    <div class="dialog-footer">
      <el-button @click="handleCancel">取消</el-button>
      <el-button
        type="primary"
        :loading="submitLoading"
        @click="handleConfirm"
      >
        确认
      </el-button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { reactive, ref, onMounted, getCurrentInstance } from 'vue';
import { queryNoShowProjectList, type ProjectOption } from '../../ProjectManagement/api';

const { $message } = getCurrentInstance().appContext.config.globalProperties;

// 定义事件
const emit = defineEmits(['save', 'cancel']);

// 表单引用
const formRef = ref();

// 提交加载状态
const submitLoading = ref(false);

// 项目选项列表
const projectOptions = ref<ProjectOption[]>([]);

// 表单数据
const formData = reactive({
  projectCode: '',
  projectName: ''
});

// 表单验证规则
const formRules = {
  projectCode: [
    { required: true, message: '请选择项目编号', trigger: 'change' }
  ],
  projectName: [
    { required: true, message: '请选择项目名称', trigger: 'change' }
  ]
};

// 加载项目选项列表
const loadProjectOptions = async () => {
  try {
    const response = await queryNoShowProjectList();
    if (response.status === '0') {
      projectOptions.value = response.data;
      console.log('加载项目选项成功:', response.data);
    } else {
      throw new Error(response.msg);
    }
  } catch (error) {
    console.error('加载项目选项失败:', error);
    $message.error('加载项目列表失败');
  }
};

// 项目编号变化处理
const handleProjectCodeChange = (value: string) => {
  if (value) {
    const selectedProject = projectOptions.value.find(item => item.projectCode === value);
    if (selectedProject) {
      formData.projectName = selectedProject.projectName;
    }
  } else {
    formData.projectName = '';
  }
};

// 项目名称变化处理
const handleProjectNameChange = (value: string) => {
  if (value) {
    const selectedProject = projectOptions.value.find(item => item.projectName === value);
    if (selectedProject) {
      formData.projectCode = selectedProject.projectCode;
    }
  } else {
    formData.projectCode = '';
  }
};

// 确认
const handleConfirm = async () => {
  try {
    const valid = await formRef.value.validate();
    if (!valid) {
      return;
    }
    submitLoading.value = true;
    // 不再调用API添加项目，而是直接触发save事件
    emit('save', {
      projectCode: formData.projectCode,
      projectName: formData.projectName,
      projectId: formData.projectCode // 传递一个projectId给父组件
    });
  } catch (error) {
    console.error('确认失败:', error);
  } finally {
    submitLoading.value = false;
  }
};

// 取消操作
const handleCancel = () => {
  emit('cancel');
};

// 组件挂载时加载数据
onMounted(() => {
  loadProjectOptions();
});

// 组件选项
defineOptions({
  name: 'AddChangeDialog'
});
</script>

<style lang="scss" scoped>
.add-project-dialog {
  padding: 20px;

  .add-project-form {
    .form-select {
      width: 100%;
    }

    :deep(.el-form-item__label) {
      color: #000000;
    }

    :deep(.el-select) {
      .el-input__inner {
        background: rgba(255, 255, 255, 0.1);
        border-color: rgba(255, 255, 255, 0.2);
        color: #000000;

        &::placeholder {
          color: rgba(0, 0, 0, 0.5);
        }
      }

      .el-input__suffix {
        .el-select__caret {
          color: rgba(0, 0, 0, 0.7);
        }
      }
    }
  }

  .dialog-footer {
    display: flex;
    justify-content: flex-end;
    gap: 12px;
    margin-top: 30px;
    padding-top: 20px;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
  }
}

// 下拉框选项样式
:deep(.el-select-dropdown) {
  background: rgba(30, 35, 50, 0.95);
  border: 1px solid rgba(255, 255, 255, 0.2);

  .el-select-dropdown__item {
    color: #000000;
    background: transparent;

    &:hover {
      background: rgba(255, 255, 255, 0.1);
    }

    &.selected {
      background: #409eff;
      color: #000000;
    }
  }
}
</style>